import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { nextTick } from 'vue'
import McpConfigurationModal from '../McpConfigurationModal.vue'
import type { McpServerConfiguration, VolumeMount, HostEntry } from '@/types/mcp'

describe('McpConfigurationModal', () => {
  let wrapper: VueWrapper<any>

  const createWrapper = (props: any = {}) => {
    return mount(McpConfigurationModal, {
      props: {
        config: null,
        ...props
      },
      global: {
        stubs: {
          // 如果需要，可以在这里添加组件存根
        }
      }
    })
  }

  beforeEach(() => {
    wrapper = createWrapper()
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  describe('Volume Mounts Configuration', () => {
    it('should render volume mounts section', () => {
      const volumeSection = wrapper.find('h4').filter(h4 => h4.text() === 'Volume Mounts')
      expect(volumeSection.exists()).toBe(true)
    })

    it('should add new volume mount when clicking add button', async () => {
      const addButton = wrapper.find('button').filter(btn => btn.text().includes('Add Volume Mount'))
      expect(addButton.exists()).toBe(true)

      await addButton.trigger('click')
      await nextTick()

      // 检查是否添加了新的volume mount输入框
      const hostPathInputs = wrapper.findAll('input[placeholder="/host/path"]')
      expect(hostPathInputs.length).toBe(1)

      const containerPathInputs = wrapper.findAll('input[placeholder="/container/path"]')
      expect(containerPathInputs.length).toBe(1)
    })

    it('should remove volume mount when clicking remove button', async () => {
      // 先添加一个volume mount
      const addButton = wrapper.find('button').filter(btn => btn.text().includes('Add Volume Mount'))
      await addButton.trigger('click')
      await nextTick()

      // 验证已添加
      let hostPathInputs = wrapper.findAll('input[placeholder="/host/path"]')
      expect(hostPathInputs.length).toBe(1)

      // 点击删除按钮
      const removeButton = wrapper.find('button').filter(btn => btn.text() === 'Remove')
      await removeButton.trigger('click')
      await nextTick()

      // 验证已删除
      hostPathInputs = wrapper.findAll('input[placeholder="/host/path"]')
      expect(hostPathInputs.length).toBe(0)
    })

    it('should bind volume mount data correctly', async () => {
      // 添加volume mount
      const addButton = wrapper.find('button').filter(btn => btn.text().includes('Add Volume Mount'))
      await addButton.trigger('click')
      await nextTick()

      // 填写数据
      const hostPathInput = wrapper.find('input[placeholder="/host/path"]')
      const containerPathInput = wrapper.find('input[placeholder="/container/path"]')
      const readOnlyCheckbox = wrapper.find('input[type="checkbox"]').filter(input => 
        input.attributes('id')?.includes('readOnly')
      )

      await hostPathInput.setValue('/test/host/path')
      await containerPathInput.setValue('/test/container/path')
      await readOnlyCheckbox.setChecked(true)
      await nextTick()

      // 验证数据绑定
      expect(hostPathInput.element.value).toBe('/test/host/path')
      expect(containerPathInput.element.value).toBe('/test/container/path')
      expect((readOnlyCheckbox.element as HTMLInputElement).checked).toBe(true)
    })

    it('should initialize with existing volume mounts when editing', () => {
      const existingConfig: McpServerConfiguration = {
        id: 1,
        name: 'test-config',
        command: 'python',
        dockerImage: 'python:3.9',
        volumeMounts: [
          { hostPath: '/existing/host', containerPath: '/existing/container', readOnly: false },
          { hostPath: '/logs/host', containerPath: '/logs/container', readOnly: true }
        ]
      }

      wrapper = createWrapper({ config: existingConfig })

      // 验证现有的volume mounts被正确加载
      const hostPathInputs = wrapper.findAll('input[placeholder="/host/path"]')
      const containerPathInputs = wrapper.findAll('input[placeholder="/container/path"]')
      
      expect(hostPathInputs.length).toBe(2)
      expect(containerPathInputs.length).toBe(2)
      
      expect(hostPathInputs[0].element.value).toBe('/existing/host')
      expect(containerPathInputs[0].element.value).toBe('/existing/container')
      expect(hostPathInputs[1].element.value).toBe('/logs/host')
      expect(containerPathInputs[1].element.value).toBe('/logs/container')
    })
  })

  describe('Host Entries Configuration', () => {
    it('should render host entries section', () => {
      const hostSection = wrapper.find('h4').filter(h4 => h4.text() === 'Host Entries')
      expect(hostSection.exists()).toBe(true)
    })

    it('should add new host entry when clicking add button', async () => {
      const addButton = wrapper.find('button').filter(btn => btn.text().includes('Add Host Entry'))
      expect(addButton.exists()).toBe(true)

      await addButton.trigger('click')
      await nextTick()

      // 检查是否添加了新的host entry输入框
      const hostnameInputs = wrapper.findAll('input[placeholder="example.com"]')
      expect(hostnameInputs.length).toBe(1)

      const ipAddressInputs = wrapper.findAll('input[placeholder="*************"]')
      expect(ipAddressInputs.length).toBe(1)
    })

    it('should remove host entry when clicking remove button', async () => {
      // 先添加一个host entry
      const addButton = wrapper.find('button').filter(btn => btn.text().includes('Add Host Entry'))
      await addButton.trigger('click')
      await nextTick()

      // 验证已添加
      let hostnameInputs = wrapper.findAll('input[placeholder="example.com"]')
      expect(hostnameInputs.length).toBe(1)

      // 点击删除按钮
      const removeButton = wrapper.find('button').filter(btn => btn.text() === 'Remove')
      await removeButton.trigger('click')
      await nextTick()

      // 验证已删除
      hostnameInputs = wrapper.findAll('input[placeholder="example.com"]')
      expect(hostnameInputs.length).toBe(0)
    })

    it('should bind host entry data correctly', async () => {
      // 添加host entry
      const addButton = wrapper.find('button').filter(btn => btn.text().includes('Add Host Entry'))
      await addButton.trigger('click')
      await nextTick()

      // 填写数据
      const hostnameInput = wrapper.find('input[placeholder="example.com"]')
      const ipAddressInput = wrapper.find('input[placeholder="*************"]')

      await hostnameInput.setValue('test.example.com')
      await ipAddressInput.setValue('********')
      await nextTick()

      // 验证数据绑定
      expect(hostnameInput.element.value).toBe('test.example.com')
      expect(ipAddressInput.element.value).toBe('********')
    })

    it('should initialize with existing host entries when editing', () => {
      const existingConfig: McpServerConfiguration = {
        id: 2,
        name: 'test-config-host',
        command: 'node',
        dockerImage: 'node:16',
        hostEntries: [
          { hostname: 'api.example.com', ipAddress: '*************' },
          { hostname: 'db.example.com', ipAddress: '*************' }
        ]
      }

      wrapper = createWrapper({ config: existingConfig })

      // 验证现有的host entries被正确加载
      const hostnameInputs = wrapper.findAll('input[placeholder="example.com"]')
      const ipAddressInputs = wrapper.findAll('input[placeholder="*************"]')
      
      expect(hostnameInputs.length).toBe(2)
      expect(ipAddressInputs.length).toBe(2)
      
      expect(hostnameInputs[0].element.value).toBe('api.example.com')
      expect(ipAddressInputs[0].element.value).toBe('*************')
      expect(hostnameInputs[1].element.value).toBe('db.example.com')
      expect(ipAddressInputs[1].element.value).toBe('*************')
    })
  })

  describe('Form Submission', () => {
    it('should emit save event with volume mounts and host entries', async () => {
      // 添加volume mount
      const addVolumeButton = wrapper.find('button').filter(btn => btn.text().includes('Add Volume Mount'))
      await addVolumeButton.trigger('click')
      await nextTick()

      // 填写volume mount数据
      const hostPathInput = wrapper.find('input[placeholder="/host/path"]')
      const containerPathInput = wrapper.find('input[placeholder="/container/path"]')
      await hostPathInput.setValue('/test/host')
      await containerPathInput.setValue('/test/container')

      // 添加host entry
      const addHostButton = wrapper.find('button').filter(btn => btn.text().includes('Add Host Entry'))
      await addHostButton.trigger('click')
      await nextTick()

      // 填写host entry数据
      const hostnameInput = wrapper.find('input[placeholder="example.com"]')
      const ipAddressInput = wrapper.find('input[placeholder="*************"]')
      await hostnameInput.setValue('test.local')
      await ipAddressInput.setValue('127.0.0.1')

      // 填写基本配置
      const nameInput = wrapper.find('input[placeholder="Configuration name"]')
      const commandInput = wrapper.find('input[placeholder="Command to run"]')
      const dockerImageInput = wrapper.find('input[placeholder="Docker image"]')
      
      await nameInput.setValue('test-config')
      await commandInput.setValue('python')
      await dockerImageInput.setValue('python:3.9')
      await nextTick()

      // 提交表单
      const saveButton = wrapper.find('button').filter(btn => btn.text() === 'Save Configuration')
      await saveButton.trigger('click')
      await nextTick()

      // 验证emit的数据
      const emittedEvents = wrapper.emitted('save')
      expect(emittedEvents).toBeTruthy()
      expect(emittedEvents!.length).toBe(1)

      const emittedConfig = emittedEvents![0][0] as McpServerConfiguration
      expect(emittedConfig.name).toBe('test-config')
      expect(emittedConfig.command).toBe('python')
      expect(emittedConfig.dockerImage).toBe('python:3.9')

      // 验证volume mounts
      expect(emittedConfig.volumeMounts).toBeTruthy()
      expect(emittedConfig.volumeMounts!.length).toBe(1)
      expect(emittedConfig.volumeMounts![0].hostPath).toBe('/test/host')
      expect(emittedConfig.volumeMounts![0].containerPath).toBe('/test/container')

      // 验证host entries
      expect(emittedConfig.hostEntries).toBeTruthy()
      expect(emittedConfig.hostEntries!.length).toBe(1)
      expect(emittedConfig.hostEntries![0].hostname).toBe('test.local')
      expect(emittedConfig.hostEntries![0].ipAddress).toBe('127.0.0.1')
    })
  })

  describe('Modal Behavior', () => {
    it('should emit close event when clicking close button', async () => {
      const closeButton = wrapper.find('button').filter(btn => 
        btn.find('svg').exists() // 关闭按钮包含SVG图标
      )
      
      await closeButton.trigger('click')
      
      const emittedEvents = wrapper.emitted('close')
      expect(emittedEvents).toBeTruthy()
      expect(emittedEvents!.length).toBe(1)
    })

    it('should show correct title for new configuration', () => {
      const title = wrapper.find('h3')
      expect(title.text()).toBe('Create New MCP Server Configuration')
    })

    it('should show correct title for editing configuration', () => {
      const existingConfig: McpServerConfiguration = {
        id: 1,
        name: 'existing-config',
        command: 'python',
        dockerImage: 'python:3.9'
      }

      wrapper = createWrapper({ config: existingConfig })
      
      const title = wrapper.find('h3')
      expect(title.text()).toBe('Edit MCP Server Configuration')
    })
  })
})
