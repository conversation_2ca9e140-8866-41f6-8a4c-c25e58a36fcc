package com.example.springvueapp.controller;

import com.example.springvueapp.model.HostEntry;
import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.model.VolumeMount;
import com.example.springvueapp.service.McpConfigurationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.security.core.Authentication;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * MCP配置控制器的单元测试
 * 重点测试Volume挂载和Host域名解析配置的API端点
 */
@ExtendWith(MockitoExtension.class)
class McpConfigurationControllerTest {

    @Mock
    private McpConfigurationService configurationService;

    @Mock
    private Authentication authentication;

    private WebTestClient webTestClient;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        McpConfigurationController controller = new McpConfigurationController(configurationService);
        webTestClient = WebTestClient.bindToController(controller).build();
        objectMapper = new ObjectMapper();
        objectMapper.findAndRegisterModules(); // 支持LocalDateTime序列化
    }

    @Test
    void testCreateConfigurationWithVolumeMounts() throws Exception {
        // 准备测试数据
        VolumeMount mount1 = new VolumeMount("/host/data", "/container/data", false);
        VolumeMount mount2 = new VolumeMount("/host/logs", "/container/logs", true);
        List<VolumeMount> volumeMounts = Arrays.asList(mount1, mount2);

        McpServerConfiguration requestConfig = McpServerConfiguration.builder()
                .name("test-volume-config")
                .description("测试Volume挂载配置")
                .command("python")
                .dockerImage("python:3.9")
                .volumeMounts(volumeMounts)
                .enabled(true)
                .build();

        McpServerConfiguration responseConfig = McpServerConfiguration.builder()
                .id(1L)
                .name("test-volume-config")
                .description("测试Volume挂载配置")
                .command("python")
                .dockerImage("python:3.9")
                .volumeMounts(volumeMounts)
                .enabled(true)
                .userId(100L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // Mock服务层 - 注意getUserId方法返回固定的1L
        when(configurationService.createConfiguration(any(McpServerConfiguration.class), eq(1L)))
                .thenReturn(Mono.just(responseConfig));

        // 执行测试
        webTestClient.post()
                .uri("/api/mcp/configurations")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(requestConfig)
                .headers(headers -> headers.set("X-User-ID", "100"))
                .exchange()
                .expectStatus().isCreated()
                .expectBody(McpServerConfiguration.class)
                .value(config -> {
                    assert config != null;
                    assert config.getId().equals(1L);
                    assert config.getName().equals("test-volume-config");
                    assert config.getVolumeMounts() != null;
                    assert config.getVolumeMounts().size() == 2;
                    
                    VolumeMount actualMount1 = config.getVolumeMounts().get(0);
                    assert actualMount1.getHostPath().equals("/host/data");
                    assert actualMount1.getContainerPath().equals("/container/data");
                    assert !actualMount1.getReadOnly();
                    
                    VolumeMount actualMount2 = config.getVolumeMounts().get(1);
                    assert actualMount2.getHostPath().equals("/host/logs");
                    assert actualMount2.getContainerPath().equals("/container/logs");
                    assert actualMount2.getReadOnly();
                });
    }

    @Test
    void testCreateConfigurationWithHostEntries() throws Exception {
        // 准备测试数据
        HostEntry entry1 = new HostEntry("example.com", "*************");
        HostEntry entry2 = new HostEntry("api.example.com", "*************");
        List<HostEntry> hostEntries = Arrays.asList(entry1, entry2);

        McpServerConfiguration requestConfig = McpServerConfiguration.builder()
                .name("test-host-config")
                .description("测试Host域名解析配置")
                .command("node")
                .dockerImage("node:16")
                .hostEntries(hostEntries)
                .enabled(true)
                .build();

        McpServerConfiguration responseConfig = McpServerConfiguration.builder()
                .id(2L)
                .name("test-host-config")
                .description("测试Host域名解析配置")
                .command("node")
                .dockerImage("node:16")
                .hostEntries(hostEntries)
                .enabled(true)
                .userId(200L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // Mock服务层 - 注意getUserId方法返回固定的1L
        when(configurationService.createConfiguration(any(McpServerConfiguration.class), eq(1L)))
                .thenReturn(Mono.just(responseConfig));

        // 执行测试
        webTestClient.post()
                .uri("/api/mcp/configurations")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(requestConfig)
                .headers(headers -> headers.set("X-User-ID", "200"))
                .exchange()
                .expectStatus().isCreated()
                .expectBody(McpServerConfiguration.class)
                .value(config -> {
                    assert config != null;
                    assert config.getId().equals(2L);
                    assert config.getName().equals("test-host-config");
                    assert config.getHostEntries() != null;
                    assert config.getHostEntries().size() == 2;
                    
                    HostEntry actualEntry1 = config.getHostEntries().get(0);
                    assert actualEntry1.getHostname().equals("example.com");
                    assert actualEntry1.getIpAddress().equals("*************");
                    
                    HostEntry actualEntry2 = config.getHostEntries().get(1);
                    assert actualEntry2.getHostname().equals("api.example.com");
                    assert actualEntry2.getIpAddress().equals("*************");
                });
    }

    @Test
    void testCreateConfigurationWithBothVolumeAndHost() throws Exception {
        // 准备测试数据 - 同时包含Volume和Host配置
        VolumeMount mount = new VolumeMount("/host/app", "/container/app", false);
        HostEntry entry = new HostEntry("database.local", "********");

        McpServerConfiguration requestConfig = McpServerConfiguration.builder()
                .name("test-complete-config")
                .description("完整配置测试")
                .command("java")
                .dockerImage("openjdk:11")
                .volumeMounts(Arrays.asList(mount))
                .hostEntries(Arrays.asList(entry))
                .enabled(true)
                .build();

        McpServerConfiguration responseConfig = McpServerConfiguration.builder()
                .id(3L)
                .name("test-complete-config")
                .description("完整配置测试")
                .command("java")
                .dockerImage("openjdk:11")
                .volumeMounts(Arrays.asList(mount))
                .hostEntries(Arrays.asList(entry))
                .enabled(true)
                .userId(300L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // Mock服务层 - 注意getUserId方法返回固定的1L
        when(configurationService.createConfiguration(any(McpServerConfiguration.class), eq(1L)))
                .thenReturn(Mono.just(responseConfig));

        // 执行测试
        webTestClient.post()
                .uri("/api/mcp/configurations")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(requestConfig)
                .headers(headers -> headers.set("X-User-ID", "300"))
                .exchange()
                .expectStatus().isCreated()
                .expectBody(McpServerConfiguration.class)
                .value(config -> {
                    assert config != null;
                    assert config.getId().equals(3L);
                    assert config.getName().equals("test-complete-config");
                    
                    // 验证Volume挂载
                    assert config.getVolumeMounts() != null;
                    assert config.getVolumeMounts().size() == 1;
                    VolumeMount actualMount = config.getVolumeMounts().get(0);
                    assert actualMount.getHostPath().equals("/host/app");
                    assert actualMount.getContainerPath().equals("/container/app");
                    assert !actualMount.getReadOnly();
                    
                    // 验证Host条目
                    assert config.getHostEntries() != null;
                    assert config.getHostEntries().size() == 1;
                    HostEntry actualEntry = config.getHostEntries().get(0);
                    assert actualEntry.getHostname().equals("database.local");
                    assert actualEntry.getIpAddress().equals("********");
                });
    }

    @Test
    void testUpdateConfigurationWithNewVolumeMounts() throws Exception {
        // 准备测试数据
        VolumeMount newMount = new VolumeMount("/host/new", "/container/new", true);
        List<VolumeMount> newVolumeMounts = Arrays.asList(newMount);

        McpServerConfiguration requestConfig = McpServerConfiguration.builder()
                .name("updated-config")
                .command("python")
                .dockerImage("python:3.10")
                .volumeMounts(newVolumeMounts)
                .enabled(true)
                .build();

        McpServerConfiguration responseConfig = McpServerConfiguration.builder()
                .id(4L)
                .name("updated-config")
                .command("python")
                .dockerImage("python:3.10")
                .volumeMounts(newVolumeMounts)
                .enabled(true)
                .userId(400L)
                .createdAt(LocalDateTime.now().minusHours(1))
                .updatedAt(LocalDateTime.now())
                .build();

        // Mock服务层 - 注意getUserId方法返回固定的1L
        when(configurationService.updateConfiguration(eq(4L), any(McpServerConfiguration.class), eq(1L)))
                .thenReturn(Mono.just(responseConfig));

        // 执行测试
        webTestClient.put()
                .uri("/api/mcp/configurations/4")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(requestConfig)
                .headers(headers -> headers.set("X-User-ID", "400"))
                .exchange()
                .expectStatus().isOk()
                .expectBody(McpServerConfiguration.class)
                .value(config -> {
                    assert config != null;
                    assert config.getId().equals(4L);
                    assert config.getName().equals("updated-config");
                    assert config.getVolumeMounts() != null;
                    assert config.getVolumeMounts().size() == 1;
                    
                    VolumeMount actualMount = config.getVolumeMounts().get(0);
                    assert actualMount.getHostPath().equals("/host/new");
                    assert actualMount.getContainerPath().equals("/container/new");
                    assert actualMount.getReadOnly();
                });
    }

    @Test
    void testGetConfigurationWithVolumeAndHost() throws Exception {
        // 准备测试数据
        VolumeMount mount = new VolumeMount("/host/data", "/container/data", false);
        HostEntry entry = new HostEntry("service.local", "***********");

        McpServerConfiguration config = McpServerConfiguration.builder()
                .id(5L)
                .name("get-test-config")
                .command("go")
                .dockerImage("golang:1.19")
                .volumeMounts(Arrays.asList(mount))
                .hostEntries(Arrays.asList(entry))
                .enabled(true)
                .userId(500L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // Mock服务层 - 注意getUserId方法返回固定的1L
        when(configurationService.getConfiguration(eq(5L), eq(1L)))
                .thenReturn(Mono.just(config));

        // 执行测试
        webTestClient.get()
                .uri("/api/mcp/configurations/5")
                .headers(headers -> headers.set("X-User-ID", "500"))
                .exchange()
                .expectStatus().isOk()
                .expectBody(McpServerConfiguration.class)
                .value(responseConfig -> {
                    assert responseConfig != null;
                    assert responseConfig.getId().equals(5L);
                    assert responseConfig.getName().equals("get-test-config");
                    
                    // 验证Volume挂载
                    assert responseConfig.getVolumeMounts() != null;
                    assert responseConfig.getVolumeMounts().size() == 1;
                    VolumeMount actualMount = responseConfig.getVolumeMounts().get(0);
                    assert actualMount.getHostPath().equals("/host/data");
                    assert actualMount.getContainerPath().equals("/container/data");
                    assert !actualMount.getReadOnly();
                    
                    // 验证Host条目
                    assert responseConfig.getHostEntries() != null;
                    assert responseConfig.getHostEntries().size() == 1;
                    HostEntry actualEntry = responseConfig.getHostEntries().get(0);
                    assert actualEntry.getHostname().equals("service.local");
                    assert actualEntry.getIpAddress().equals("***********");
                });
    }

    @Test
    void testGetAllConfigurationsWithVolumeAndHost() throws Exception {
        // 准备测试数据
        VolumeMount mount1 = new VolumeMount("/host/app1", "/container/app1", false);
        HostEntry entry1 = new HostEntry("app1.local", "********");

        VolumeMount mount2 = new VolumeMount("/host/app2", "/container/app2", true);
        HostEntry entry2 = new HostEntry("app2.local", "********");

        McpServerConfiguration config1 = McpServerConfiguration.builder()
                .id(6L)
                .name("config-1")
                .command("python")
                .dockerImage("python:3.9")
                .volumeMounts(Arrays.asList(mount1))
                .hostEntries(Arrays.asList(entry1))
                .enabled(true)
                .userId(600L)
                .build();

        McpServerConfiguration config2 = McpServerConfiguration.builder()
                .id(7L)
                .name("config-2")
                .command("node")
                .dockerImage("node:16")
                .volumeMounts(Arrays.asList(mount2))
                .hostEntries(Arrays.asList(entry2))
                .enabled(true)
                .userId(600L)
                .build();

        // Mock服务层 - 注意getUserId方法返回固定的1L
        when(configurationService.getUserConfigurations(eq(1L)))
                .thenReturn(Flux.just(config1, config2));

        // 执行测试
        webTestClient.get()
                .uri("/api/mcp/configurations")
                .headers(headers -> headers.set("X-User-ID", "600"))
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(McpServerConfiguration.class)
                .value(configs -> {
                    assert configs != null;
                    assert configs.size() == 2;
                    
                    // 验证第一个配置
                    McpServerConfiguration actualConfig1 = configs.get(0);
                    assert actualConfig1.getId().equals(6L);
                    assert actualConfig1.getVolumeMounts().size() == 1;
                    assert actualConfig1.getHostEntries().size() == 1;
                    
                    // 验证第二个配置
                    McpServerConfiguration actualConfig2 = configs.get(1);
                    assert actualConfig2.getId().equals(7L);
                    assert actualConfig2.getVolumeMounts().size() == 1;
                    assert actualConfig2.getHostEntries().size() == 1;
                });
    }


}
