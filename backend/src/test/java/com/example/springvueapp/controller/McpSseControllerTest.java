package com.example.springvueapp.controller;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.service.McpSseService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

/**
 * McpSseController单元测试
 */
@ExtendWith(MockitoExtension.class)
class McpSseControllerTest {

    @Mock
    private McpSseService sseService;

    private McpSseController controller;

    @BeforeEach
    void setUp() {
        controller = new McpSseController(sseService);
    }

    @Test
    void testEstablishSseSession_Success() {
        // 准备测试数据
        String mcpServerName = "test-server";
        Authentication authentication = new UsernamePasswordAuthenticationToken("1", null);

        Flux<ServerSentEvent<String>> eventStream = Flux.just(
                ServerSentEvent.<String>builder()
                        .event("endpoint")
                        .data("{\"sessionId\":\"test-session\",\"messageEndpoint\":\"/api/mcp/sse/message\"}")
                        .build()
        );

        // 模拟服务行为
        when(sseService.createSession(mcpServerName, 1L)).thenReturn(Mono.just(eventStream));

        // 执行测试 - 直接调用控制器方法
        Mono<ResponseEntity<Flux<ServerSentEvent<String>>>> result = controller.establishSseSession(mcpServerName, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(200, response.getStatusCode().value());
                    assertNotNull(response.getBody());
                    assertNotNull(response.getHeaders().get("Cache-Control"));
                    assertNotNull(response.getHeaders().get("Connection"));
                })
                .verifyComplete();

        // 验证方法调用
        verify(sseService).createSession(mcpServerName, 1L);
    }

    @Test
    void testEstablishSseSession_ServiceError() {
        // 准备测试数据
        String mcpServerName = "test-server";
        Authentication authentication = new UsernamePasswordAuthenticationToken("1", null);

        // 模拟服务错误
        when(sseService.createSession(mcpServerName, 1L)).thenReturn(Mono.error(new RuntimeException("Service error")));

        // 执行测试
        Mono<ResponseEntity<Flux<ServerSentEvent<String>>>> result = controller.establishSseSession(mcpServerName, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(500, response.getStatusCode().value());
                })
                .verifyComplete();

        // 验证方法调用
        verify(sseService).createSession(mcpServerName, 1L);
    }

    @Test
    void testSendMessage_Success() throws Exception {
        // 准备测试数据
        String sessionId = "test-session";
        JsonRpcRequest request = new JsonRpcRequest("tools/list", Map.of());
        Authentication authentication = new UsernamePasswordAuthenticationToken("1", null);

        // 模拟服务行为
        when(sseService.sendMessage(sessionId, request)).thenReturn(Mono.empty());

        // 执行测试
        Mono<ResponseEntity<Map<String, Object>>> result = controller.sendMessage(sessionId, request, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(200, response.getStatusCode().value());
                    assertNotNull(response.getBody());
                    assertEquals("sent", response.getBody().get("status"));
                })
                .verifyComplete();

        // 验证方法调用
        verify(sseService).sendMessage(sessionId, request);
    }

    @Test
    void testSendMessage_InvalidRequest() {
        // 准备测试数据 - 缺少sessionId
        JsonRpcRequest request = new JsonRpcRequest("test", Map.of());
        Authentication authentication = new UsernamePasswordAuthenticationToken("1", null);

        // 执行测试
        Mono<ResponseEntity<Map<String, Object>>> result = controller.sendMessage(null, request, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(400, response.getStatusCode().value());
                    assertNotNull(response.getBody());
                    assertEquals("sessionId is required", response.getBody().get("error"));
                })
                .verifyComplete();

        // 验证没有调用服务
        verifyNoInteractions(sseService);
    }

    @Test
    void testCloseSession_Success() {
        // 准备测试数据
        String sessionId = "test-session";
        Authentication authentication = new UsernamePasswordAuthenticationToken("1", null);

        // 模拟服务行为
        when(sseService.closeSession(sessionId))
                .thenReturn(Mono.empty());

        // 执行测试
        Mono<ResponseEntity<Map<String, Object>>> result =
                controller.closeSession(sessionId, authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(200, response.getStatusCode().value());
                    assertNotNull(response.getBody());
                    assertEquals("closed", response.getBody().get("status"));
                })
                .verifyComplete();

        // 验证方法调用
        verify(sseService).closeSession(sessionId);
    }

    @Test
    void testGetStatus_Success() {
        // 准备测试数据
        Authentication authentication = new UsernamePasswordAuthenticationToken("1", null);

        // 模拟服务行为
        when(sseService.getActiveSessionCount()).thenReturn(5);
        when(sseService.getUserSessions(1L)).thenReturn(Map.of());

        // 执行测试
        Mono<ResponseEntity<Map<String, Object>>> result =
                controller.getStatus(authentication);

        // 验证结果
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(200, response.getStatusCode().value());
                    assertNotNull(response.getBody());
                    assertEquals(5, response.getBody().get("activeSessionCount"));
                    assertEquals(0, response.getBody().get("userSessions"));
                })
                .verifyComplete();

        // 验证方法调用
        verify(sseService).getActiveSessionCount();
        verify(sseService).getUserSessions(1L);
    }
}
