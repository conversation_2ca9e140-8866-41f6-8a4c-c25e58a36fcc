package com.example.springvueapp.sandbox;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SandboxConfig的单元测试
 * 测试Volume挂载和Host域名解析配置的数据模型
 */
class SandboxConfigTest {

    @Test
    void testBuilderWithVolumeMounts() {
        // 创建Volume挂载配置
        SandboxConfig.VolumeMount mount1 = new SandboxConfig.VolumeMount();
        mount1.setHostPath("/host/data");
        mount1.setContainerPath("/container/data");
        mount1.setReadOnly(false);

        SandboxConfig.VolumeMount mount2 = new SandboxConfig.VolumeMount();
        mount2.setHostPath("/host/logs");
        mount2.setContainerPath("/container/logs");
        mount2.setReadOnly(true);

        List<SandboxConfig.VolumeMount> volumeMounts = Arrays.asList(mount1, mount2);

        // 使用Builder创建配置
        SandboxConfig config = SandboxConfig.builder()
                .name("test-sandbox")
                .dockerImage("ubuntu:20.04")
                .command("/bin/bash")
                .volumeMounts(volumeMounts)
                .build();

        // 验证配置
        assertNotNull(config);
        assertEquals("test-sandbox", config.getName());
        assertEquals("ubuntu:20.04", config.getDockerImage());
        assertEquals("/bin/bash", config.getCommand());
        
        List<SandboxConfig.VolumeMount> actualMounts = config.getVolumeMounts();
        assertNotNull(actualMounts);
        assertEquals(2, actualMounts.size());
        
        // 验证第一个Volume挂载
        SandboxConfig.VolumeMount actualMount1 = actualMounts.get(0);
        assertEquals("/host/data", actualMount1.getHostPath());
        assertEquals("/container/data", actualMount1.getContainerPath());
        assertFalse(actualMount1.getReadOnly());
        
        // 验证第二个Volume挂载
        SandboxConfig.VolumeMount actualMount2 = actualMounts.get(1);
        assertEquals("/host/logs", actualMount2.getHostPath());
        assertEquals("/container/logs", actualMount2.getContainerPath());
        assertTrue(actualMount2.getReadOnly());
    }

    @Test
    void testBuilderWithHostEntries() {
        // 创建Host域名解析配置
        SandboxConfig.HostEntry entry1 = new SandboxConfig.HostEntry();
        entry1.setHostname("example.com");
        entry1.setIpAddress("*************");

        SandboxConfig.HostEntry entry2 = new SandboxConfig.HostEntry();
        entry2.setHostname("api.example.com");
        entry2.setIpAddress("*************");

        List<SandboxConfig.HostEntry> hostEntries = Arrays.asList(entry1, entry2);

        // 使用Builder创建配置
        SandboxConfig config = SandboxConfig.builder()
                .name("test-sandbox")
                .dockerImage("ubuntu:20.04")
                .command("/bin/bash")
                .hostEntries(hostEntries)
                .build();

        // 验证配置
        assertNotNull(config);
        assertEquals("test-sandbox", config.getName());
        
        List<SandboxConfig.HostEntry> actualEntries = config.getHostEntries();
        assertNotNull(actualEntries);
        assertEquals(2, actualEntries.size());
        
        // 验证第一个Host条目
        SandboxConfig.HostEntry actualEntry1 = actualEntries.get(0);
        assertEquals("example.com", actualEntry1.getHostname());
        assertEquals("*************", actualEntry1.getIpAddress());
        
        // 验证第二个Host条目
        SandboxConfig.HostEntry actualEntry2 = actualEntries.get(1);
        assertEquals("api.example.com", actualEntry2.getHostname());
        assertEquals("*************", actualEntry2.getIpAddress());
    }

    @Test
    void testBuilderWithCompleteConfiguration() {
        // 创建完整的配置
        Map<String, String> environment = new HashMap<>();
        environment.put("ENV_VAR1", "value1");
        environment.put("ENV_VAR2", "value2");

        List<String> arguments = Arrays.asList("--verbose", "--config=/app/config.yml");

        SandboxConfig.ResourceLimits resourceLimits = new SandboxConfig.ResourceLimits();
        resourceLimits.setMemoryLimitBytes(1024L * 1024L * 512L); // 512MB
        resourceLimits.setCpuLimit(1.0);

        SandboxConfig.NetworkConfig networkConfig = new SandboxConfig.NetworkConfig();
        networkConfig.setEnableNetworking(true);
        networkConfig.setAllowedHosts(Arrays.asList("example.com", "api.example.com"));

        SandboxConfig.VolumeMount volumeMount = new SandboxConfig.VolumeMount();
        volumeMount.setHostPath("/host/data");
        volumeMount.setContainerPath("/container/data");
        volumeMount.setReadOnly(false);

        SandboxConfig.HostEntry hostEntry = new SandboxConfig.HostEntry();
        hostEntry.setHostname("database.local");
        hostEntry.setIpAddress("********");

        // 使用Builder创建完整配置
        SandboxConfig config = SandboxConfig.builder()
                .id("sandbox-123")
                .name("complete-sandbox")
                .description("完整的沙箱配置测试")
                .dockerImage("ubuntu:20.04")
                .command("/bin/bash")
                .arguments(arguments)
                .environment(environment)
                .workingDirectory("/app")
                .resourceLimits(resourceLimits)
                .networkConfig(networkConfig)
                .volumeMounts(Arrays.asList(volumeMount))
                .hostEntries(Arrays.asList(hostEntry))
                .timeoutSeconds(600)
                .autoRestart(true)
                .build();

        // 验证所有配置
        assertNotNull(config);
        assertEquals("sandbox-123", config.getId());
        assertEquals("complete-sandbox", config.getName());
        assertEquals("完整的沙箱配置测试", config.getDescription());
        assertEquals("ubuntu:20.04", config.getDockerImage());
        assertEquals("/bin/bash", config.getCommand());
        assertEquals("/app", config.getWorkingDirectory());
        assertEquals(600, config.getTimeoutSeconds());
        assertTrue(config.getAutoRestart());

        // 验证参数
        assertEquals(2, config.getArguments().size());
        assertTrue(config.getArguments().contains("--verbose"));
        assertTrue(config.getArguments().contains("--config=/app/config.yml"));

        // 验证环境变量
        assertEquals(2, config.getEnvironment().size());
        assertEquals("value1", config.getEnvironment().get("ENV_VAR1"));
        assertEquals("value2", config.getEnvironment().get("ENV_VAR2"));

        // 验证资源限制
        assertNotNull(config.getResourceLimits());
        assertEquals(1024L * 1024L * 512L, config.getResourceLimits().getMemoryLimitBytes());
        assertEquals(1.0, config.getResourceLimits().getCpuLimit());

        // 验证网络配置
        assertNotNull(config.getNetworkConfig());
        assertTrue(config.getNetworkConfig().getEnableNetworking());
        assertEquals(2, config.getNetworkConfig().getAllowedHosts().size());

        // 验证Volume挂载
        assertNotNull(config.getVolumeMounts());
        assertEquals(1, config.getVolumeMounts().size());
        SandboxConfig.VolumeMount actualMount = config.getVolumeMounts().get(0);
        assertEquals("/host/data", actualMount.getHostPath());
        assertEquals("/container/data", actualMount.getContainerPath());
        assertFalse(actualMount.getReadOnly());

        // 验证Host条目
        assertNotNull(config.getHostEntries());
        assertEquals(1, config.getHostEntries().size());
        SandboxConfig.HostEntry actualEntry = config.getHostEntries().get(0);
        assertEquals("database.local", actualEntry.getHostname());
        assertEquals("********", actualEntry.getIpAddress());
    }

    @Test
    void testVolumeMountConstructors() {
        // 测试默认构造函数
        SandboxConfig.VolumeMount mount1 = new SandboxConfig.VolumeMount();
        assertNull(mount1.getHostPath());
        assertNull(mount1.getContainerPath());
        assertNull(mount1.getReadOnly());

        // 测试setter方法
        mount1.setHostPath("/test/host");
        mount1.setContainerPath("/test/container");
        mount1.setReadOnly(true);

        assertEquals("/test/host", mount1.getHostPath());
        assertEquals("/test/container", mount1.getContainerPath());
        assertTrue(mount1.getReadOnly());
    }

    @Test
    void testHostEntryConstructors() {
        // 测试默认构造函数
        SandboxConfig.HostEntry entry1 = new SandboxConfig.HostEntry();
        assertNull(entry1.getHostname());
        assertNull(entry1.getIpAddress());

        // 测试带参数的构造函数
        SandboxConfig.HostEntry entry2 = new SandboxConfig.HostEntry("test.com", "*******");
        assertEquals("test.com", entry2.getHostname());
        assertEquals("*******", entry2.getIpAddress());

        // 测试setter方法
        entry1.setHostname("example.org");
        entry1.setIpAddress("*******");

        assertEquals("example.org", entry1.getHostname());
        assertEquals("*******", entry1.getIpAddress());
    }

    @Test
    void testBuilderWithNullValues() {
        // 测试Builder处理null值
        SandboxConfig config = SandboxConfig.builder()
                .name("test-sandbox")
                .dockerImage("ubuntu:20.04")
                .volumeMounts(null)
                .hostEntries(null)
                .build();

        assertNotNull(config);
        assertEquals("test-sandbox", config.getName());
        assertEquals("ubuntu:20.04", config.getDockerImage());
        assertNull(config.getVolumeMounts());
        assertNull(config.getHostEntries());
    }

    @Test
    void testSettersAndGetters() {
        // 测试直接使用setter和getter
        SandboxConfig config = new SandboxConfig();
        
        SandboxConfig.VolumeMount mount = new SandboxConfig.VolumeMount();
        mount.setHostPath("/test");
        mount.setContainerPath("/test");
        
        SandboxConfig.HostEntry entry = new SandboxConfig.HostEntry();
        entry.setHostname("test.local");
        entry.setIpAddress("127.0.0.1");
        
        config.setVolumeMounts(Arrays.asList(mount));
        config.setHostEntries(Arrays.asList(entry));

        assertNotNull(config.getVolumeMounts());
        assertEquals(1, config.getVolumeMounts().size());
        assertEquals("/test", config.getVolumeMounts().get(0).getHostPath());

        assertNotNull(config.getHostEntries());
        assertEquals(1, config.getHostEntries().size());
        assertEquals("test.local", config.getHostEntries().get(0).getHostname());
    }
}
