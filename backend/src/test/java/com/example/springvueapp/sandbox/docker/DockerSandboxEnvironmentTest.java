package com.example.springvueapp.sandbox.docker;

import com.example.springvueapp.sandbox.SandboxConfig;
import com.example.springvueapp.sandbox.SandboxInstance;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.command.CreateContainerCmd;
import com.github.dockerjava.api.command.CreateContainerResponse;
import com.github.dockerjava.api.model.Bind;
import com.github.dockerjava.api.model.HostConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Docker沙箱环境的单元测试
 * 重点测试Volume挂载和Host域名解析配置功能
 */
@ExtendWith(MockitoExtension.class)
class DockerSandboxEnvironmentTest {

    @Mock
    private DockerClient dockerClient;

    @Mock
    private CreateContainerCmd createContainerCmd;

    @Mock
    private CreateContainerResponse createContainerResponse;

    private DockerSandboxEnvironment dockerSandboxEnvironment;

    @BeforeEach
    void setUp() {
        dockerSandboxEnvironment = new DockerSandboxEnvironment();
        // 使用反射设置私有字段
        try {
            var field = DockerSandboxEnvironment.class.getDeclaredField("dockerClient");
            field.setAccessible(true);
            field.set(dockerSandboxEnvironment, dockerClient);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testCreateSandboxWithVolumeMounts() {
        // 准备测试数据
        SandboxConfig config = SandboxConfig.builder()
                .name("test-sandbox")
                .dockerImage("ubuntu:20.04")
                .command("/bin/bash")
                .volumeMounts(Arrays.asList(
                        createVolumeMount("/host/data", "/container/data", false),
                        createVolumeMount("/host/logs", "/container/logs", true)
                ))
                .build();

        // 设置Mock行为
        when(dockerClient.createContainerCmd(anyString())).thenReturn(createContainerCmd);
        when(createContainerCmd.withCmd(any(String[].class))).thenReturn(createContainerCmd);
        when(createContainerCmd.withEnv(any(List.class))).thenReturn(createContainerCmd);
        when(createContainerCmd.withWorkingDir(anyString())).thenReturn(createContainerCmd);
        when(createContainerCmd.withHostConfig(any(HostConfig.class))).thenReturn(createContainerCmd);
        when(createContainerCmd.withStdinOpen(anyBoolean())).thenReturn(createContainerCmd);
        when(createContainerCmd.withAttachStdin(anyBoolean())).thenReturn(createContainerCmd);
        when(createContainerCmd.withAttachStdout(anyBoolean())).thenReturn(createContainerCmd);
        when(createContainerCmd.withAttachStderr(anyBoolean())).thenReturn(createContainerCmd);
        when(createContainerCmd.exec()).thenReturn(createContainerResponse);
        when(createContainerResponse.getId()).thenReturn("container-id-123");

        // 执行测试
        StepVerifier.create(dockerSandboxEnvironment.createSandbox(config))
                .assertNext(instance -> {
                    assertNotNull(instance);
                    assertEquals("container-id-123", instance.getId());
                    assertEquals(config, instance.getConfig());
                })
                .verifyComplete();

        // 验证Volume挂载配置
        ArgumentCaptor<HostConfig> hostConfigCaptor = ArgumentCaptor.forClass(HostConfig.class);
        verify(createContainerCmd).withHostConfig(hostConfigCaptor.capture());
        
        HostConfig capturedHostConfig = hostConfigCaptor.getValue();
        assertNotNull(capturedHostConfig);
        
        Bind[] binds = capturedHostConfig.getBinds();
        assertNotNull(binds);
        assertEquals(2, binds.length);
        
        // 验证第一个Volume挂载（读写）
        assertEquals("/host/data", binds[0].getPath());
        assertEquals("/container/data", binds[0].getVolume().getPath());
        assertFalse(binds[0].getAccessMode().toString().contains("ro"));
        
        // 验证第二个Volume挂载（只读）
        assertEquals("/host/logs", binds[1].getPath());
        assertEquals("/container/logs", binds[1].getVolume().getPath());
        assertTrue(binds[1].getAccessMode().toString().contains("ro"));
    }

    @Test
    void testCreateSandboxWithHostEntries() {
        // 准备测试数据
        SandboxConfig config = SandboxConfig.builder()
                .name("test-sandbox")
                .dockerImage("ubuntu:20.04")
                .command("/bin/bash")
                .hostEntries(Arrays.asList(
                        createHostEntry("example.com", "*************"),
                        createHostEntry("api.example.com", "*************")
                ))
                .build();

        // 设置Mock行为
        setupMockCreateContainer();

        // 执行测试
        StepVerifier.create(dockerSandboxEnvironment.createSandbox(config))
                .assertNext(instance -> {
                    assertNotNull(instance);
                    assertEquals("container-id-123", instance.getId());
                })
                .verifyComplete();

        // 验证Host域名解析配置
        ArgumentCaptor<HostConfig> hostConfigCaptor = ArgumentCaptor.forClass(HostConfig.class);
        verify(createContainerCmd).withHostConfig(hostConfigCaptor.capture());
        
        HostConfig capturedHostConfig = hostConfigCaptor.getValue();
        assertNotNull(capturedHostConfig);
        
        String[] extraHosts = capturedHostConfig.getExtraHosts();
        assertNotNull(extraHosts);
        assertEquals(2, extraHosts.length);
        assertEquals("example.com:*************", extraHosts[0]);
        assertEquals("api.example.com:*************", extraHosts[1]);
    }

    @Test
    void testCreateSandboxWithBothVolumeMountsAndHostEntries() {
        // 准备测试数据
        SandboxConfig config = SandboxConfig.builder()
                .name("test-sandbox")
                .dockerImage("ubuntu:20.04")
                .command("/bin/bash")
                .volumeMounts(Arrays.asList(
                        createVolumeMount("/host/data", "/container/data", false)
                ))
                .hostEntries(Arrays.asList(
                        createHostEntry("example.com", "*************")
                ))
                .build();

        // 设置Mock行为
        setupMockCreateContainer();

        // 执行测试
        StepVerifier.create(dockerSandboxEnvironment.createSandbox(config))
                .assertNext(instance -> {
                    assertNotNull(instance);
                    assertEquals("container-id-123", instance.getId());
                })
                .verifyComplete();

        // 验证同时配置了Volume和Host
        ArgumentCaptor<HostConfig> hostConfigCaptor = ArgumentCaptor.forClass(HostConfig.class);
        verify(createContainerCmd).withHostConfig(hostConfigCaptor.capture());
        
        HostConfig capturedHostConfig = hostConfigCaptor.getValue();
        assertNotNull(capturedHostConfig);
        
        // 验证Volume挂载
        Bind[] binds = capturedHostConfig.getBinds();
        assertNotNull(binds);
        assertEquals(1, binds.length);
        assertEquals("/host/data", binds[0].getPath());
        
        // 验证Host条目
        String[] extraHosts = capturedHostConfig.getExtraHosts();
        assertNotNull(extraHosts);
        assertEquals(1, extraHosts.length);
        assertEquals("example.com:*************", extraHosts[0]);
    }

    @Test
    void testCreateSandboxWithEmptyVolumeMounts() {
        // 准备测试数据 - 空的Volume挂载列表
        SandboxConfig config = SandboxConfig.builder()
                .name("test-sandbox")
                .dockerImage("ubuntu:20.04")
                .command("/bin/bash")
                .volumeMounts(Arrays.asList())
                .build();

        // 设置Mock行为
        setupMockCreateContainer();

        // 执行测试
        StepVerifier.create(dockerSandboxEnvironment.createSandbox(config))
                .assertNext(instance -> assertNotNull(instance))
                .verifyComplete();

        // 验证没有设置Volume挂载
        ArgumentCaptor<HostConfig> hostConfigCaptor = ArgumentCaptor.forClass(HostConfig.class);
        verify(createContainerCmd).withHostConfig(hostConfigCaptor.capture());
        
        HostConfig capturedHostConfig = hostConfigCaptor.getValue();
        assertNotNull(capturedHostConfig);
        
        Bind[] binds = capturedHostConfig.getBinds();
        // 空列表应该不设置binds或者设置为空数组
        assertTrue(binds == null || binds.length == 0);
    }

    @Test
    void testCreateSandboxWithNullHostEntries() {
        // 准备测试数据 - null的Host条目
        SandboxConfig config = SandboxConfig.builder()
                .name("test-sandbox")
                .dockerImage("ubuntu:20.04")
                .command("/bin/bash")
                .hostEntries(null)
                .build();

        // 设置Mock行为
        setupMockCreateContainer();

        // 执行测试
        StepVerifier.create(dockerSandboxEnvironment.createSandbox(config))
                .assertNext(instance -> assertNotNull(instance))
                .verifyComplete();

        // 验证没有设置Host条目
        ArgumentCaptor<HostConfig> hostConfigCaptor = ArgumentCaptor.forClass(HostConfig.class);
        verify(createContainerCmd).withHostConfig(hostConfigCaptor.capture());
        
        HostConfig capturedHostConfig = hostConfigCaptor.getValue();
        assertNotNull(capturedHostConfig);
        
        String[] extraHosts = capturedHostConfig.getExtraHosts();
        // null应该不设置extraHosts
        assertNull(extraHosts);
    }

    // 辅助方法
    private SandboxConfig.VolumeMount createVolumeMount(String hostPath, String containerPath, boolean readOnly) {
        SandboxConfig.VolumeMount mount = new SandboxConfig.VolumeMount();
        mount.setHostPath(hostPath);
        mount.setContainerPath(containerPath);
        mount.setReadOnly(readOnly);
        return mount;
    }

    private SandboxConfig.HostEntry createHostEntry(String hostname, String ipAddress) {
        SandboxConfig.HostEntry entry = new SandboxConfig.HostEntry();
        entry.setHostname(hostname);
        entry.setIpAddress(ipAddress);
        return entry;
    }

    private void setupMockCreateContainer() {
        when(dockerClient.createContainerCmd(anyString())).thenReturn(createContainerCmd);
        when(createContainerCmd.withCmd(any(String[].class))).thenReturn(createContainerCmd);
        when(createContainerCmd.withEnv(any(List.class))).thenReturn(createContainerCmd);
        when(createContainerCmd.withWorkingDir(anyString())).thenReturn(createContainerCmd);
        when(createContainerCmd.withHostConfig(any(HostConfig.class))).thenReturn(createContainerCmd);
        when(createContainerCmd.withStdinOpen(anyBoolean())).thenReturn(createContainerCmd);
        when(createContainerCmd.withAttachStdin(anyBoolean())).thenReturn(createContainerCmd);
        when(createContainerCmd.withAttachStdout(anyBoolean())).thenReturn(createContainerCmd);
        when(createContainerCmd.withAttachStderr(anyBoolean())).thenReturn(createContainerCmd);
        when(createContainerCmd.exec()).thenReturn(createContainerResponse);
        when(createContainerResponse.getId()).thenReturn("container-id-123");
    }
}
