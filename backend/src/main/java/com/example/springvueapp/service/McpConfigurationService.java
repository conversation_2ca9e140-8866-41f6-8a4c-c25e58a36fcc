package com.example.springvueapp.service;

import com.example.springvueapp.entity.McpServerConfigurationEntity;
import com.example.springvueapp.mapper.McpConfigurationMapper;
import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.repository.McpServerConfigurationRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 管理 MCP 服务器配置的服务
 */
@Service
public class McpConfigurationService {

    private static final Logger log = LoggerFactory.getLogger(McpConfigurationService.class);

    private final McpServerConfigurationRepository configurationRepository;
    private final McpConfigurationMapper configurationMapper;

    public McpConfigurationService(McpServerConfigurationRepository configurationRepository,
                                   McpConfigurationMapper configurationMapper) {
        this.configurationRepository = configurationRepository;
        this.configurationMapper = configurationMapper;
    }

    /**
     * Create a new MCP server configuration
     */
    public Mono<McpServerConfiguration> createConfiguration(McpServerConfiguration config, Long userId) {
        return validateConfiguration(config)
                .then(configurationRepository.existsByNameAndUserId(config.getName(), userId))
                .flatMap(exists -> {
                    if (exists) {
                        return Mono.error(new RuntimeException("Configuration with name '" + config.getName() + "' already exists"));
                    }

                    config.setUserId(userId);
                    config.setCreatedAt(LocalDateTime.now());
                    config.setUpdatedAt(LocalDateTime.now());

                    // 转换为Entity保存到数据库
                    McpServerConfigurationEntity entity = configurationMapper.toEntity(config);
                    return configurationRepository.save(entity)
                            .map(configurationMapper::toDto);
                })
                .doOnSuccess(saved -> log.info("Created MCP configuration: {} for user: {}", saved.getName(), userId))
                .doOnError(error -> log.error("Failed to create MCP configuration", error));
    }

    /**
     * Update an existing MCP server configuration
     */
    public Mono<McpServerConfiguration> updateConfiguration(Long configId, McpServerConfiguration config, Long userId) {
        return configurationRepository.findById(configId)
                .switchIfEmpty(Mono.error(new RuntimeException("Configuration not found")))
                .flatMap(existing -> {
                    if (!existing.getUserId().equals(userId)) {
                        return Mono.error(new RuntimeException("Access denied"));
                    }

                    // 转换DTO为Entity进行更新
                    config.setId(configId);
                    config.setUserId(userId);
                    config.setCreatedAt(existing.getCreatedAt());
                    config.setUpdatedAt(LocalDateTime.now());

                    McpServerConfigurationEntity updatedEntity = configurationMapper.toEntity(config);

                    return validateConfiguration(config)
                            .then(configurationRepository.save(updatedEntity))
                            .map(configurationMapper::toDto);
                })
                .doOnSuccess(updated -> log.info("Updated MCP configuration: {}", updated.getName()))
                .doOnError(error -> log.error("Failed to update MCP configuration: {}", configId, error));
    }

    /**
     * Delete an MCP server configuration
     */
    public Mono<Void> deleteConfiguration(Long configId, Long userId) {
        return configurationRepository.findById(configId)
                .switchIfEmpty(Mono.error(new RuntimeException("Configuration not found")))
                .flatMap(config -> {
                    if (!config.getUserId().equals(userId)) {
                        return Mono.error(new RuntimeException("Access denied"));
                    }

                    return configurationRepository.delete(config);
                })
                .doOnSuccess(v -> log.info("Deleted MCP configuration: {}", configId))
                .doOnError(error -> log.error("Failed to delete MCP configuration: {}", configId, error));
    }

    /**
     * Get a configuration by ID
     */
    public Mono<McpServerConfiguration> getConfiguration(Long configId, Long userId) {
        return configurationRepository.findById(configId)
                .switchIfEmpty(Mono.error(new RuntimeException("Configuration not found")))
                .flatMap(entity -> {
                    if (!entity.getUserId().equals(userId)) {
                        return Mono.error(new RuntimeException("Access denied"));
                    }
                    return Mono.just(configurationMapper.toDto(entity));
                });
    }

    /**
     * Get all configurations for a user
     */
    public Flux<McpServerConfiguration> getUserConfigurations(Long userId) {
        return configurationRepository.findByUserId(userId)
                .map(configurationMapper::toDto)
                .doOnNext(config -> log.debug("Retrieved configuration: {} for user: {}", config.getName(), userId));
    }

    /**
     * Get a configuration by name for a user
     */
    public Mono<McpServerConfiguration> getConfigurationByName(String name, Long userId) {
        return configurationRepository.findByNameAndUserId(name, userId)
                .switchIfEmpty(Mono.error(new RuntimeException("Configuration not found: " + name)))
                .map(configurationMapper::toDto)
                .doOnNext(config -> log.debug("Retrieved configuration by name: {} for user: {}", name, userId));
    }

    /**
     * Get enabled configurations for a user
     */
    public Flux<McpServerConfiguration> getEnabledConfigurations(Long userId) {
        return configurationRepository.findByUserIdAndEnabled(userId, true)
                .map(configurationMapper::toDto);
    }

    /**
     * Toggle configuration enabled status
     */
    public Mono<McpServerConfiguration> toggleEnabled(Long configId, Long userId) {
        return configurationRepository.findById(configId)
                .switchIfEmpty(Mono.error(new RuntimeException("Configuration not found")))
                .flatMap(entity -> {
                    if (!entity.getUserId().equals(userId)) {
                        return Mono.error(new RuntimeException("Access denied"));
                    }

                    entity.setEnabled(!entity.getEnabled());
                    entity.setUpdatedAt(LocalDateTime.now());

                    return configurationRepository.save(entity)
                            .map(configurationMapper::toDto);
                })
                .doOnSuccess(config -> log.info("Toggled enabled status for configuration: {} to: {}",
                        config.getName(), config.getEnabled()));
    }

    private Mono<Void> validateConfiguration(McpServerConfiguration config) {
        return Mono.fromRunnable(() -> {
            if (config.getName() == null || config.getName().trim().isEmpty()) {
                throw new RuntimeException("Configuration name is required");
            }

            if (config.getCommand() == null || config.getCommand().trim().isEmpty()) {
                throw new RuntimeException("Command is required");
            }

            if (config.getDockerImage() == null || config.getDockerImage().trim().isEmpty()) {
                throw new RuntimeException("Docker image is required");
            }

            // Additional validation logic can be added here
        });
    }
}
