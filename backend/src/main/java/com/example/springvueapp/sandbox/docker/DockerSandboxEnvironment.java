package com.example.springvueapp.sandbox.docker;

import com.example.springvueapp.sandbox.SandboxConfig;
import com.example.springvueapp.sandbox.SandboxEnvironment;
import com.example.springvueapp.sandbox.SandboxInstance;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.command.CreateContainerResponse;
import com.github.dockerjava.api.model.AccessMode;
import com.github.dockerjava.api.model.Bind;
import com.github.dockerjava.api.model.HostConfig;
import com.github.dockerjava.api.model.Volume;
import com.github.dockerjava.core.DefaultDockerClientConfig;
import com.github.dockerjava.core.DockerClientConfig;
import com.github.dockerjava.core.DockerClientImpl;
import com.github.dockerjava.httpclient5.ApacheDockerHttpClient;
import com.github.dockerjava.transport.DockerHttpClient;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 基于 Docker 的沙箱环境实现
 */
public class DockerSandboxEnvironment implements SandboxEnvironment {

    private static final Logger log = LoggerFactory.getLogger(DockerSandboxEnvironment.class);

    private DockerClient dockerClient;
    private final Map<String, SandboxInstance> sandboxes = new ConcurrentHashMap<>();

    @PostConstruct
    public void initialize() {
        try {
            // 更新配置构建方式
            DockerClientConfig config = DefaultDockerClientConfig.createDefaultConfigBuilder()
                    .withDockerHost("unix:///var/run/docker.sock")
                    .build();

            // 更新 HTTP 客户端构建方式
            DockerHttpClient httpClient = new ApacheDockerHttpClient.Builder()
                    .dockerHost(config.getDockerHost())
                    .sslConfig(config.getSSLConfig())
                    .maxConnections(100)
                    .connectionTimeout(Duration.ofSeconds(30))
                    .responseTimeout(Duration.ofSeconds(45))
                    .build();

            // 更新 Docker 客户端创建方式
            dockerClient = DockerClientImpl.getInstance(config, httpClient);

            log.info("Docker client initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize Docker client", e);
            throw new RuntimeException("Docker client initialization failed", e);
        }
    }

    @PreDestroy
    public void shutdown() {
        cleanup().block();
        if (dockerClient != null) {
            try {
                dockerClient.close();
            } catch (Exception e) {
                log.error("Error closing Docker client", e);
            }
        }
    }

    @Override
    public Mono<SandboxInstance> createSandbox(SandboxConfig config) {
        return Mono.fromCallable(() -> {
            log.info("Creating Docker sandbox with config: {}", config.getName());

            // Build container creation command
            CreateContainerResponse container = createContainer(config);

            SandboxInstance instance = new DockerSandboxInstance(
                    container.getId(),
                    config,
                    dockerClient
            );

            sandboxes.put(instance.getId(), instance);
            log.info("Created Docker sandbox: {}", instance.getId());

            return instance;
        })
        .onErrorMap(e -> new RuntimeException("Failed to create Docker sandbox", e));
    }

    private CreateContainerResponse createContainer(SandboxConfig config) {
        List<String> cmd = new ArrayList<>();
        cmd.add(config.getCommand());
        if (config.getArguments() != null) {
            cmd.addAll(config.getArguments());
        }

        HostConfig hostConfig = HostConfig.newHostConfig()
                .withAutoRemove(true)
                .withNetworkMode("none"); // Isolated by default

        // Set resource limits
        if (config.getResourceLimits() != null) {
            if (config.getResourceLimits().getMemoryLimitBytes() != null) {
                hostConfig.withMemory(config.getResourceLimits().getMemoryLimitBytes());
            }
            if (config.getResourceLimits().getCpuLimit() != null) {
                hostConfig.withCpuQuota(config.getResourceLimits().getCpuLimit().longValue() * 100000);
                hostConfig.withCpuPeriod(100000L);
            }
        }

        // Configure volumes
        if (config.getVolumeMounts() != null) {
            List<Bind> binds = new ArrayList<>();
            for (SandboxConfig.VolumeMount mount : config.getVolumeMounts()) {
                AccessMode accessMode = mount.getReadOnly() != null && mount.getReadOnly()
                        ? AccessMode.ro : AccessMode.rw;
                binds.add(new Bind(mount.getHostPath(), new Volume(mount.getContainerPath()), accessMode));
            }
            hostConfig.withBinds(binds);
        }

        // Configure host entries
        if (config.getHostEntries() != null && !config.getHostEntries().isEmpty()) {
            List<String> extraHosts = new ArrayList<>();
            for (SandboxConfig.HostEntry hostEntry : config.getHostEntries()) {
                if (hostEntry.getHostname() != null && hostEntry.getIpAddress() != null) {
                    extraHosts.add(hostEntry.getHostname() + ":" + hostEntry.getIpAddress());
                }
            }
            if (!extraHosts.isEmpty()) {
                hostConfig.withExtraHosts(extraHosts.toArray(new String[0]));
            }
        }

        return dockerClient.createContainerCmd(config.getDockerImage())
                .withCmd(cmd)
                .withEnv(buildEnvironmentList(config.getEnvironment()))
                .withWorkingDir(config.getWorkingDirectory())
                .withHostConfig(hostConfig)
                .withStdinOpen(true)
                .withAttachStdin(true)
                .withAttachStdout(true)
                .withAttachStderr(true)
                .exec();
    }

    private List<String> buildEnvironmentList(Map<String, String> environment) {
        if (environment == null) {
            return new ArrayList<>();
        }

        List<String> envList = new ArrayList<>();
        environment.forEach((key, value) -> envList.add(key + "=" + value));
        return envList;
    }

    @Override
    public Mono<SandboxInstance> getSandbox(String sandboxId) {
        return Mono.fromCallable(() -> sandboxes.get(sandboxId));
    }

    @Override
    public Mono<Map<String, SandboxInstance>> listSandboxes() {
        return Mono.fromCallable(() -> Map.copyOf(sandboxes));
    }

    @Override
    public Mono<Void> destroySandbox(String sandboxId) {
        return Mono.fromRunnable(() -> {
            SandboxInstance instance = sandboxes.remove(sandboxId);
            if (instance != null) {
                instance.destroy().block();
            }
        });
    }

    @Override
    public Mono<Void> cleanup() {
        return Mono.fromRunnable(() -> {
            log.info("Cleaning up {} Docker sandboxes", sandboxes.size());
            sandboxes.values().forEach(instance -> {
                try {
                    instance.destroy().block();
                } catch (Exception e) {
                    log.error("Error destroying sandbox: {}", instance.getId(), e);
                }
            });
            sandboxes.clear();
        });
    }

    @Override
    public String getType() {
        return "docker";
    }

    @Override
    public Mono<Boolean> isHealthy() {
        return Mono.fromCallable(() -> {
            try {
                dockerClient.pingCmd().exec();
                return true;
            } catch (Exception e) {
                log.error("Docker health check failed", e);
                return false;
            }
        });
    }
}
